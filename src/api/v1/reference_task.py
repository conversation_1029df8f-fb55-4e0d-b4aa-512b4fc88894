#!/usr/bin/env python3
"""
参考任务执行API接口

基于参考任务的动作列表，让模型参考执行新的用例
"""

import threading
import uuid
from typing import Dict

from fastapi import APIRouter
from loguru import logger
from pydantic import Field

from src.api.v1.dto import UITaskCreateRequest
from src.application.reference_task_application import reference_task_application
from src.domain.ui_task.mobile.android.adb_connection_manager import connect_device, disconnect_device
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler
from src.infra.utils import get_current_env
from src.schema import const

router = APIRouter(tags=["reference-task"])

# 存储后台任务
background_tasks_registry: Dict[str, threading.Thread] = {}


class ReferenceTaskExecuteRequest(UITaskCreateRequest):
    """参考任务执行请求 - 继承UITaskCreateRequest，只增加参考任务ID"""
    reference_task_id: str = Field(..., description="参考任务ID，用于获取参考动作列表")


def execute_reference_task_background(
        task_id: str,
        request: ReferenceTaskExecuteRequest
):
    """
    后台执行参考任务
    
    Args:
        task_id: 任务ID
        request: 参考任务执行请求
    """
    try:
        logger.info(f"[{task_id}] 🚀 Background execution started for reference task: {task_id}")

        # 开始任务执行（更新数据库状态）
        reference_task_application.start_task_execution(task_id)

        # 设置ADB键盘（异步执行）
        device_id = request.device.android.url
        keyboard_setup_success = False
        try:
            keyboard_setup_success = reference_task_application.setup_adb_keyboards(task_id, device_id)
            if keyboard_setup_success:
                logger.info(f"[{task_id}] ✅ ADB keyboards setup successful")
            else:
                logger.error(f"[{task_id}] ❌ ADB keyboards setup failed - all keyboards unavailable")
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error setting up ADB keyboards: {str(e)}")

        # 如果所有键盘都设置失败，标记任务失败并结束
        if not keyboard_setup_success:
            error_message = "键盘设置失败：所有ADB键盘（FastInputIME、AdbIME）都无法启用，任务无法继续执行"
            logger.error(f"[{task_id}] {error_message}")

            # 更新任务状态为失败
            reference_task_application.complete_task_execution(
                task_id=task_id,
                success=False,
                error_message=error_message
            )

            return  # 直接返回，不执行测试用例

        # 执行参考任务
        result = reference_task_application.execute_reference_task(
            task_id=task_id,
            request=request
        )

        # 检查任务是否被停止
        was_stopped = reference_task_application.is_task_stopped(task_id)

        if was_stopped:
            logger.info(f"[{task_id}] ⏹️ Reference task was stopped by user")
        elif result.get("success", False):
            logger.info(f"[{task_id}] ✅ Reference task execution completed successfully")
        else:
            logger.error(f"[{task_id}] ❌ Reference task execution failed: {result.get('message', 'Unknown error')}")

    except Exception as e:
        logger.error(f"[{task_id}] ❌ Reference task execution failed with exception: {str(e)}")
        # 使用异常处理器处理错误
        TaskExceptionHandler.handle_task_exception(task_id, e, "reference_task_execution")
        
    finally:
        # 清理ADB键盘设置
        try:
            reference_task_application.cleanup_adb_keyboards(task_id)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error cleaning up keyboard: {str(e)}")
        
        # 断开ADB设备连接
        try:
            device_id = request.device.android.url
            if device_id:
                disconnect_device(device_id, task_id)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error disconnecting device: {str(e)}")

        # 注销任务
        try:
            reference_task_application.unregister_task(task_id)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error unregistering task: {str(e)}")

        # 清理后台任务注册
        if task_id in background_tasks_registry:
            del background_tasks_registry[task_id]


@router.post("/reference-task/execute")
async def execute_reference_task(request: ReferenceTaskExecuteRequest):
    """
    执行参考任务

    Args:
        request: 参考任务执行请求

    Returns:
        任务创建结果，包含task_id
    """

    if const.NO_PROD_ENV == get_current_env():
        task_id = uuid.uuid4().hex
    else:
        task_id = request.task_id

    try:
        # 解析设备ID - 使用与ui-task相同的方法
        device_id = request.device.android.url

        # 验证参考任务是否存在
        reference_task = reference_task_application.get_task_by_id(request.reference_task_id)
        if not reference_task:
            return {
                "code": 1,
                "message": f"Reference task {request.reference_task_id} not found",
                "data": {
                    "task_id": task_id
                }
            }

        # 创建任务到数据库
        reference_task_application.create_reference_task_from_request(
            task_id=task_id,
            request=request,
            device_id=device_id
        )

        # 注册任务到停止管理器
        reference_task_application.register_task(task_id, request.task_name)

        # 连接ADB设备（如果是远程设备）
        try:
            connect_success = connect_device(device_id, task_id)
            if connect_success:
                logger.info(f"[{task_id}] ✅ Device connection successful")
            else:
                # 设备连接失败，更新任务状态为失败并结束任务
                error_message = f"设备连接失败：无法连接到设备 {device_id}，任务无法继续执行"
                logger.error(f"[{task_id}] {error_message}")

                # 更新任务状态为失败
                reference_task_application.complete_task_execution(
                    task_id=task_id,
                    success=False,
                    error_message=error_message
                )

                # 注销任务
                reference_task_application.unregister_task(task_id)

                # 返回失败结果
                return {
                    "code": 1,
                    "message": error_message,
                    "data": {
                        "task_id": task_id
                    }
                }
        except Exception as e:
            error_message = f"设备连接异常：{str(e)}"
            logger.error(f"[{task_id}] ❌ Error connecting device: {error_message}")

            # 更新任务状态为失败
            reference_task_application.complete_task_execution(
                task_id=task_id,
                success=False,
                error_message=error_message
            )
            # 注销任务
            reference_task_application.unregister_task(task_id)

            # 返回失败结果
            return {
                "code": 1,
                "message": error_message,
                "data": {
                    "task_id": task_id
                }
            }

        logger.info(f"[{task_id}] 🚀 Starting reference task execution: {request.task_name} (Task ID: {task_id})")

        # 启动后台执行线程
        background_thread = threading.Thread(
            target=execute_reference_task_background,
            args=(task_id, request),
            daemon=True
        )

        # 注册后台任务
        background_tasks_registry[task_id] = background_thread

        # 启动线程
        background_thread.start()

        # 立即返回任务ID和启动状态（按照新的响应格式）
        return {
            "code": 0,
            "message": "",
            "data": {
                "task_id": task_id
            }
        }

    except Exception as e:
        # 记录详细的错误信息
        logger.error(f"[{task_id}] ❌ Failed to create reference task: {str(e)}")
        return {
            "code": 1,
            "message": f"Failed to create reference task: {str(e)}",
            "data": {
                "task_id": task_id
            }
        }



