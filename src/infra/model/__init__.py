from functools import cache

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel
from openai import OpenAI

from config.globalconfig import get_or_create_settings_ins

@cache
def get_chat_model(model_name: str = "default") -> BaseChatModel:
    config = get_or_create_settings_ins()
    models = config.models
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)
    llm = config.llms[model_cfg]
    return init_chat_model(
        llm.model,
        model_provider=llm.provider,
        **(llm.external_args or {}),
    )
